import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ForaChat } from '../operations';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { logger } from '../utils/Logger';

// Future Microsoft Teams interface using Bot Framework SDK
export class TeamsInterface implements MessageInterface {
  private chatService: ChatService;
  private botAdapter?: any; // Would be BotFrameworkAdapter
  private sessions: Map<string, any> = new Map(); // Cache sessions by user ID

  constructor(chatService: ChatService) {
    this.chatService = chatService;
    // this.botAdapter = new BotFrameworkAdapter({
    //   appId: process.env.MICROSOFT_APP_ID,
    //   appPassword: process.env.MICROSOFT_APP_PASSWORD
    // }); // Future implementation
  }

  private async getOrCreateSession(userId: string, tenantId?: string, channelId?: string): Promise<any> {
    const sessionKey = `${tenantId}_${userId}`;
    
    // Check cache first
    if (this.sessions.has(sessionKey)) {
      const session = this.sessions.get(sessionKey);
      // Update session activity
      try {
        await ForaChat.updateSessionActivity(session.id);
        return session;
      } catch (error) {
        // Session might be expired, remove from cache
        this.sessions.delete(sessionKey);
      }
    }

    // Try to get existing session from database
    try {
      const userIdentifier = `teams_${tenantId}_${userId}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'teams');
      let session = await handle.getResult();

      if (!session) {
        // Create new session
        const sessionRequest: SessionCreateRequest = {
          userIdentifier,
          channel: 'teams',
          metadata: {
            userId,
            tenantId,
            channelId,
            platform: 'teams'
          }
        };

        const createHandle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await createHandle.getResult();
        logger.info(`Created new Teams session for user ${userId} in tenant ${tenantId}: ${session.id}`);
      } else {
        // Update existing session activity
        await ForaChat.updateSessionActivity(session.id);
        logger.info(`Restored Teams session for user ${userId} in tenant ${tenantId}: ${session.id}`);
      }

      // Cache the session
      this.sessions.set(sessionKey, session);
      return session;
    } catch (error) {
      logger.error(`Error managing Teams session for user ${userId}`, error);
      throw error;
    }
  }

  async sendMessage(message: string): Promise<void> {
    // Future implementation with Bot Framework SDK
    logger.debug(`Teams would send: ${message}`);
    
    /*
    await this.botAdapter.sendActivity(context, {
      type: 'message',
      text: message,
      // Additional Teams-specific formatting
    });
    */
  }

  async receiveMessage(): Promise<string> {
    // This would be handled by Bot Framework activity handler in a real implementation
    throw new Error('Teams receiveMessage should be handled by Bot Framework activity handler');
  }

  formatResponse(response: ChatResponse): string {
    // Format for Teams - use Teams' adaptive cards or rich formatting
    let formatted = `**${response.theme}**\n\n`;
    
    response.reply.forEach((message, index) => {
      // Use Teams emoji and formatting
      const characterEmoji = this.getCharacterEmoji(message.character);
      formatted += `${characterEmoji} **${message.character}:** ${message.text}\n\n`;
    });
    
    if (response.skills && response.skills.length > 0) {
      formatted += `🎯 **Skills:** ${response.skills.join(', ')}`;
    }
    
    return formatted;
  }

  private getCharacterEmoji(character: string): string {
    const emojiMap: Record<string, string> = {
      'Fora': '💼',
      'Jan': '👩‍💻',
      'Lou': '👨‍💼',
      'user': '💬',
      'system': '⚙️'
    };
    return emojiMap[character] || '💬';
  }

  // Teams activity handler for incoming messages
  async handleTeamsActivity(context: any): Promise<void> {
    try {
      if (context.activity.type !== 'message' || !context.activity.text) {
        return; // Ignore non-message activities
      }

      const { from, channelData, text } = context.activity;
      const userId = from.id;
      const tenantId = channelData?.tenant?.id;
      const channelId = context.activity.channelId;
      
      // Get or create session for this user
      const session = await this.getOrCreateSession(userId, tenantId, channelId);
      
      let result;
      if (session.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        result = await handle.getResult();
        
        // Update session with new conversation ID
        if (result.conversationId) {
          await ForaChat.updateSessionConversation(session.id, result.conversationId);
          session.conversation_id = result.conversationId;
          this.sessions.set(`${tenantId}_${userId}`, session); // Update cache
        }
      }
      
      const formattedResponse = this.formatResponse(result);
      
      // Send response back to Teams
      /*
      await context.sendActivity({
        type: 'message',
        text: formattedResponse
      });
      */
      
    } catch (error) {
      logger.error(`Error processing Teams activity`, error);

      // Send error message to user
      /*
      await context.sendActivity({
        type: 'message',
        text: 'Sorry, I had trouble processing your message. Please try again.'
      });
      */
    }
  }

  // Method to get session info for a user (useful for debugging/admin)
  async getSessionInfo(userId: string, tenantId: string): Promise<any> {
    try {
      const userIdentifier = `teams_${tenantId}_${userId}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'teams');
      return await handle.getResult();
    } catch (error) {
      logger.error(`Error getting Teams session info for user ${userId}`, error);
      return null;
    }
  }

  // Method to clear session cache (useful for testing or admin operations)
  clearSessionCache(userId?: string, tenantId?: string): void {
    if (userId && tenantId) {
      const sessionKey = `${tenantId}_${userId}`;
      this.sessions.delete(sessionKey);
      logger.info(`Cleared Teams session cache for user ${userId} in tenant ${tenantId}`);
    } else {
      this.sessions.clear();
      logger.info('Cleared all Teams session cache');
    }
  }

  // Teams messaging extension handler
  async handleMessagingExtension(context: any): Promise<any> {
    try {
      const { from, channelData } = context.activity;
      const userId = from.id;
      const tenantId = channelData?.tenant?.id;
      const query = context.activity.value?.commandId;
      
      if (!query) {
        return {
          composeExtension: {
            type: 'result',
            attachmentLayout: 'list',
            attachments: [{
              contentType: 'application/vnd.microsoft.card.adaptive',
              content: {
                type: 'AdaptiveCard',
                body: [{
                  type: 'TextBlock',
                  text: 'Please provide a query to search for workplace advice.'
                }]
              }
            }]
          }
        };
      }
      
      // Get or create session for this user
      const session = await this.getOrCreateSession(userId, tenantId);
      
      let result;
      if (session.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(query, session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(query);
        result = await handle.getResult();
        
        // Update session with new conversation ID
        if (result.conversationId) {
          await ForaChat.updateSessionConversation(session.id, result.conversationId);
        }
      }
      
      const formattedResponse = this.formatResponse(result);
      
      return {
        composeExtension: {
          type: 'result',
          attachmentLayout: 'list',
          attachments: [{
            contentType: 'application/vnd.microsoft.card.adaptive',
            content: {
              type: 'AdaptiveCard',
              body: [{
                type: 'TextBlock',
                text: formattedResponse,
                wrap: true
              }]
            }
          }]
        }
      };
      
    } catch (error) {
      logger.error(`Error processing Teams messaging extension`, error);
      return {
        composeExtension: {
          type: 'result',
          attachmentLayout: 'list',
          attachments: [{
            contentType: 'application/vnd.microsoft.card.adaptive',
            content: {
              type: 'AdaptiveCard',
              body: [{
                type: 'TextBlock',
                text: 'Sorry, I had trouble processing your request. Please try again.'
              }]
            }
          }]
        }
      };
    }
  }
}
