import { GoogleGenerativeAI } from "@google/generative-ai";
import { DBOS } from "@dbos-inc/dbos-sdk";
import { LLMService } from './LLMService';
import { ChatResponse } from '../models/types';
import config from '../config';
import { logger } from '../utils/Logger';
import * as fs from 'fs/promises';
import * as path from 'path';

export class GeminiLLMService extends LLMService {
  private genAI: GoogleGenerativeAI;
  private static schemaCache: any = null;

  constructor() {
    super();
    this.genAI = new GoogleGenerativeAI(config.llm.apiKey);
  }

  private static async loadResponseSchema(): Promise<any> {
    if (GeminiLLMService.schemaCache) {
      return GeminiLLMService.schemaCache;
    }

    try {
      const schemaPath = path.join(__dirname, '..', '..', 'prompts', 'output.json');
      const schemaContent = await fs.readFile(schemaPath, 'utf-8');
      const schema = JSON.parse(schemaContent);

      // Convert JSON Schema format to Gemini API format
      const geminiSchema = GeminiLLMService.convertToGeminiSchema(schema);

      // Cache the converted schema for future use
      GeminiLLMService.schemaCache = geminiSchema;

      logger.info('Loaded and converted response schema from prompts/output.json');
      return geminiSchema;
    } catch (error) {
      logger.error(`Failed to load response schema`, error);
      // Return null to fall back to no schema validation
      return null;
    }
  }

  private static convertToGeminiSchema(schema: any): any {
    if (!schema || typeof schema !== 'object') {
      return schema;
    }

    const converted: any = {};

    // Convert type to uppercase
    if (schema.type) {
      converted.type = schema.type.toUpperCase();
    }

    // Copy other properties and recursively convert nested schemas
    for (const [key, value] of Object.entries(schema)) {
      if (key === 'type') {
        // Already handled above
        continue;
      } else if (key === 'properties' && typeof value === 'object') {
        converted.properties = {};
        for (const [propKey, propValue] of Object.entries(value as object)) {
          converted.properties[propKey] = GeminiLLMService.convertToGeminiSchema(propValue);
        }
      } else if (key === 'items') {
        converted.items = GeminiLLMService.convertToGeminiSchema(value);
      } else {
        converted[key] = value;
      }
    }

    return converted;
  }

  async generate(systemPrompt: string, userPrompt: string): Promise<ChatResponse> {
    return GeminiLLMService.generateStatic(systemPrompt, userPrompt);
  }

  @DBOS.step()
  static async generateStatic(systemPrompt: string, userPrompt: string): Promise<ChatResponse> {
    const genAI = new GoogleGenerativeAI(config.llm.apiKey);
    const model = genAI.getGenerativeModel({ model: config.llm.model });

    // Load the response schema
    const responseSchema = await GeminiLLMService.loadResponseSchema();

    // Build generation config with schema if available
    const generationConfig: any = {
      maxOutputTokens: config.llm.maxTokens,
      responseMimeType: "application/json",
    };

    if (responseSchema) {
      generationConfig.responseSchema = responseSchema;
      logger.info('Using response schema for structured output');
    } else {
      logger.warn('No response schema available, using unstructured JSON output');
    }

    const chat = model.startChat({
      history: [
        {
          role: "user",
          parts: [{ text: systemPrompt }],
        },
        {
          role: "model",
          parts: [{ text: "Okay, I'm ready. What's the user's request?" }],
        },
      ],
      generationConfig,
    });

    const result = await chat.sendMessage(userPrompt);
    const response = result.response;
    const text = response.text();

    try {
      const parsed = JSON.parse(text);

      // Validate response structure
      if (!GeminiLLMService.isValidChatResponse(parsed)) {
        logger.error('LLM response failed validation despite schema constraint');
        logger.error(`Invalid response structure: ${JSON.stringify(parsed)}`);
        throw new Error('Invalid response structure from LLM');
      }

      logger.info('LLM response successfully validated against schema');
      return parsed;
    } catch (error) {
      if (error instanceof SyntaxError) {
        logger.error(`Failed to parse LLM response as JSON`, error);
        logger.error(`Raw response was: ${text}`);
        throw new Error(`Invalid JSON response from LLM: ${(error as Error).message}`);
      } else {
        // Re-throw validation errors
        throw error;
      }
    }
  }

  private static isValidChatResponse(obj: any): obj is ChatResponse {
    return (
      obj &&
      typeof obj === 'object' &&
      Array.isArray(obj.reply) &&
      Array.isArray(obj.skills) &&
      typeof obj.theme === 'string' &&
      obj.reply.every((msg: any) =>
        typeof msg.character === 'string' &&
        typeof msg.text === 'string' &&
        typeof msg.delay === 'number'
      )
    );
  }
}
